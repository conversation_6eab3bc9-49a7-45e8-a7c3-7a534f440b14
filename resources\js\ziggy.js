const Ziggy = {"url":"http:\/\/192.168.1.84:8000","port":8000,"defaults":{},"routes":{"home":{"uri":"\/","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"projects.index":{"uri":"projects","methods":["GET","HEAD"]},"projects.create":{"uri":"projects\/create","methods":["GET","HEAD"]},"projects.store":{"uri":"projects","methods":["POST"]},"projects.show":{"uri":"projects\/{project}","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"projects.edit":{"uri":"projects\/{project}\/edit","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"projects.update":{"uri":"projects\/{project}","methods":["PUT","PATCH"],"parameters":["project"],"bindings":{"project":"id"}},"projects.destroy":{"uri":"projects\/{project}","methods":["DELETE"],"parameters":["project"],"bindings":{"project":"id"}},"projects.members.search":{"uri":"projects\/{project}\/members\/search","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"projects.members.store":{"uri":"projects\/{project}\/members","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"projects.members.update":{"uri":"projects\/{project}\/members\/{user}","methods":["PUT"],"parameters":["project","user"],"bindings":{"project":"id","user":"id"}},"projects.members.destroy":{"uri":"projects\/{project}\/members\/{user}","methods":["DELETE"],"parameters":["project","user"],"bindings":{"project":"id","user":"id"}},"boards.index":{"uri":"projects\/{project}\/boards","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"boards.create":{"uri":"projects\/{project}\/boards\/create","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"boards.store":{"uri":"projects\/{project}\/boards","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"boards.show":{"uri":"projects\/{project}\/boards\/{board}","methods":["GET","HEAD"],"parameters":["project","board"],"bindings":{"project":"id","board":"id"}},"boards.edit":{"uri":"projects\/{project}\/boards\/{board}\/edit","methods":["GET","HEAD"],"parameters":["project","board"],"bindings":{"project":"id","board":"id"}},"boards.update":{"uri":"projects\/{project}\/boards\/{board}","methods":["PUT"],"parameters":["project","board"],"bindings":{"project":"id","board":"id"}},"boards.destroy":{"uri":"projects\/{project}\/boards\/{board}","methods":["DELETE"],"parameters":["project","board"],"bindings":{"project":"id","board":"id"}},"lists.store":{"uri":"projects\/{project}\/boards\/{board}\/lists","methods":["POST"],"parameters":["project","board"],"bindings":{"project":"id","board":"id"}},"lists.update":{"uri":"projects\/{project}\/boards\/{board}\/lists\/{list}","methods":["PUT"],"parameters":["project","board","list"],"bindings":{"project":"id","board":"id","list":"id"}},"lists.destroy":{"uri":"projects\/{project}\/boards\/{board}\/lists\/{list}","methods":["DELETE"],"parameters":["project","board","list"],"bindings":{"project":"id","board":"id","list":"id"}},"lists.positions":{"uri":"projects\/{project}\/boards\/{board}\/lists\/positions","methods":["POST"],"parameters":["project","board"],"bindings":{"project":"id","board":"id"}},"tasks.index":{"uri":"projects\/{project}\/tasks","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"tasks.create":{"uri":"projects\/{project}\/boards\/{board}\/lists\/{list}\/tasks\/create","methods":["GET","HEAD"],"parameters":["project","board","list"],"bindings":{"project":"id","board":"id","list":"id"}},"tasks.store":{"uri":"projects\/{project}\/boards\/{board}\/lists\/{list}\/tasks","methods":["POST"],"parameters":["project","board","list"],"bindings":{"project":"id","board":"id","list":"id"}},"tasks.show":{"uri":"projects\/{project}\/tasks\/{task}","methods":["GET","HEAD"],"parameters":["project","task"],"bindings":{"project":"id","task":"id"}},"tasks.edit":{"uri":"projects\/{project}\/tasks\/{task}\/edit","methods":["GET","HEAD"],"parameters":["project","task"],"bindings":{"project":"id","task":"id"}},"tasks.update":{"uri":"projects\/{project}\/tasks\/{task}","methods":["PUT"],"parameters":["project","task"],"bindings":{"project":"id","task":"id"}},"tasks.update-due-date":{"uri":"projects\/{project}\/tasks\/{task}\/due-date","methods":["PATCH"],"parameters":["project","task"],"bindings":{"project":"id","task":"id"}},"tasks.destroy":{"uri":"projects\/{project}\/tasks\/{task}","methods":["DELETE"],"parameters":["project","task"],"bindings":{"project":"id","task":"id"}},"tasks.positions":{"uri":"projects\/{project}\/tasks\/positions","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"comments.store":{"uri":"projects\/{project}\/tasks\/{task}\/comments","methods":["POST"],"parameters":["project","task"],"bindings":{"project":"id","task":"id"}},"comments.update":{"uri":"projects\/{project}\/tasks\/{task}\/comments\/{comment}","methods":["PUT"],"parameters":["project","task","comment"],"bindings":{"project":"id","task":"id","comment":"id"}},"comments.destroy":{"uri":"projects\/{project}\/tasks\/{task}\/comments\/{comment}","methods":["DELETE"],"parameters":["project","task","comment"],"bindings":{"project":"id","task":"id","comment":"id"}},"labels.index":{"uri":"projects\/{project}\/labels","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"labels.create":{"uri":"projects\/{project}\/labels\/create","methods":["GET","HEAD"],"parameters":["project"],"bindings":{"project":"id"}},"labels.store":{"uri":"projects\/{project}\/labels","methods":["POST"],"parameters":["project"],"bindings":{"project":"id"}},"labels.edit":{"uri":"projects\/{project}\/labels\/{label}\/edit","methods":["GET","HEAD"],"parameters":["project","label"],"bindings":{"project":"id","label":"id"}},"labels.update":{"uri":"projects\/{project}\/labels\/{label}","methods":["PUT"],"parameters":["project","label"],"bindings":{"project":"id","label":"id"}},"labels.destroy":{"uri":"projects\/{project}\/labels\/{label}","methods":["DELETE"],"parameters":["project","label"],"bindings":{"project":"id","label":"id"}},"my-tasks":{"uri":"my-tasks","methods":["GET","HEAD"]},"inbox":{"uri":"inbox","methods":["GET","HEAD"]},"inbox.tasks.store":{"uri":"inbox\/tasks","methods":["POST"]},"inbox.tasks.update":{"uri":"inbox\/tasks\/{task}","methods":["PUT"],"parameters":["task"],"bindings":{"task":"id"}},"inbox.tasks.destroy":{"uri":"inbox\/tasks\/{task}","methods":["DELETE"],"parameters":["task"],"bindings":{"task":"id"}},"inbox.tasks.move-to-project":{"uri":"inbox\/tasks\/{task}\/move-to-project","methods":["POST"],"parameters":["task"],"bindings":{"task":"id"}},"team":{"uri":"team","methods":["GET","HEAD"]},"calendar":{"uri":"calendar","methods":["GET","HEAD"]},"reports":{"uri":"reports","methods":["GET","HEAD"]},"time-tracking":{"uri":"time-tracking","methods":["GET","HEAD"]},"documents":{"uri":"documents","methods":["GET","HEAD"]},"messages":{"uri":"messages","methods":["GET","HEAD"]},"favorites":{"uri":"favorites","methods":["GET","HEAD"]},"favorites.toggle":{"uri":"favorites\/toggle","methods":["POST"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"sidebar.settings":{"uri":"settings\/sidebar","methods":["GET","HEAD"]},"sidebar-preferences.show":{"uri":"settings\/sidebar-preferences","methods":["GET","HEAD"]},"sidebar-preferences.update":{"uri":"settings\/sidebar-preferences","methods":["PATCH"]},"sidebar-preferences.reset":{"uri":"settings\/sidebar-preferences\/reset","methods":["POST"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
