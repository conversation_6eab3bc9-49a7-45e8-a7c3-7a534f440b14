<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Traits\Favoritable;
use App\Traits\RenumbersIdsAfterDeletion;

class Task extends Model
{
    use HasFactory, RenumbersIdsAfterDeletion, Favoritable;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
        'description',
        'list_id',
        'project_id',
        'created_by',
        'position',
        'priority',
        'status',
        'estimate',
        'due_date',
        'start_date',
        'duration_days',
        'completed_at',
        'is_archived',
        'is_inbox',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'position' => 'integer',
        'estimate' => 'integer',
        'due_date' => 'datetime',
        'start_date' => 'datetime',
        'duration_days' => 'integer',
        'completed_at' => 'datetime',
        'is_archived' => 'boolean',
        'is_inbox' => 'boolean',
    ];

    /**
     * Get the list that owns the task.
     */
    public function list(): BelongsTo
    {
        return $this->belongsTo(TaskList::class, 'list_id');
    }

    /**
     * Get the project that owns the task.
     * This relationship is optional for inbox tasks.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Scope a query to only include inbox tasks.
     */
    public function scopeInbox($query)
    {
        return $query->where('is_inbox', true);
    }

    /**
     * Get the user that created the task.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the users assigned to the task.
     */
    public function assignees(): BelongsToMany
    {
        return $this->belongsToMany(User::class)
            ->withTimestamps();
    }

    /**
     * Get the labels for the task.
     */
    public function labels(): BelongsToMany
    {
        return $this->belongsToMany(Label::class)
            ->withTimestamps();
    }

    /**
     * Get the comments for the task.
     */
    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }
}
