import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Project, Task } from '@/types/project-manager';
import { router } from '@inertiajs/react';
import { useState } from 'react';
import {
    X,
    Calendar,
    Clock,
    User,
    Tag,
    AlertCircle,
    CheckCircle2,
    Timer,
    FileText,
    Users,
    Briefcase,
    Flag,
    TrendingUp,
    Activity,
    ChevronDown
} from 'lucide-react';

interface TaskDetailModalProps {
    task: any;
    project?: Project;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export default function TaskDetailModal({ task, project, open, onOpenChange }: TaskDetailModalProps) {
    if (!task) return null;

    // State for managing task updates
    const [currentStatus, setCurrentStatus] = useState(task.status);
    const [isUpdating, setIsUpdating] = useState(false);
    const [showSuccess, setShowSuccess] = useState(false);

    // Update local state when task changes
    useState(() => {
        setCurrentStatus(task.status);
    }, [task.status]);

    // Handle status change
    const handleStatusChange = async (newStatus: string) => {
        if (newStatus === currentStatus || isUpdating) return;

        setIsUpdating(true);
        setCurrentStatus(newStatus);

        try {
            // Update the task status
            await router.put(route('tasks.update', { project: project?.id || task.project_id, task: task.id }), {
                title: task.title,
                description: task.description || '',
                priority: task.priority,
                status: newStatus,
                estimate: task.estimate,
                due_date: task.due_date,
                start_date: task.start_date,
                duration_days: task.duration_days,
                list_id: task.list_id,
                assignee_ids: task.assignees?.map((a: any) => a.id) || [],
                label_ids: task.labels?.map((l: any) => l.id) || [],
                is_archived: task.is_archived || false,
            }, {
                preserveState: true,
                preserveScroll: true,
                onSuccess: () => {
                    // Show success indicator briefly
                    setShowSuccess(true);
                    setTimeout(() => setShowSuccess(false), 2000);
                },
                onError: (errors) => {
                    console.error('Failed to update task status:', errors);
                    // Revert status on error
                    setCurrentStatus(task.status);
                },
                onFinish: () => {
                    setIsUpdating(false);
                }
            });
        } catch (error) {
            console.error('Error updating task status:', error);
            setCurrentStatus(task.status);
            setIsUpdating(false);
        }
    };

    // Get priority color styling
    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'urgent':
                return 'text-red-700 bg-red-100 border-red-300 dark:text-red-300 dark:bg-red-950/30 dark:border-red-700';
            case 'high':
                return 'text-orange-700 bg-orange-100 border-orange-300 dark:text-orange-300 dark:bg-orange-950/30 dark:border-orange-700';
            case 'medium':
                return 'text-yellow-700 bg-yellow-100 border-yellow-300 dark:text-yellow-300 dark:bg-yellow-950/30 dark:border-yellow-700';
            case 'low':
                return 'text-green-700 bg-green-100 border-green-300 dark:text-green-300 dark:bg-green-950/30 dark:border-green-700';
            default:
                return 'text-blue-700 bg-blue-100 border-blue-300 dark:text-blue-300 dark:bg-blue-950/30 dark:border-blue-700';
        }
    };

    // Get status color and icon
    const getStatusInfo = (status: string) => {
        switch (status) {
            case 'done':
                return {
                    color: 'text-green-600 bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800',
                    icon: CheckCircle2,
                    label: 'Completed'
                };
            case 'in_progress':
                return {
                    color: 'text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800',
                    icon: Timer,
                    label: 'In Progress'
                };
            case 'to_do':
            default:
                return {
                    color: 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-950/20 dark:border-gray-800',
                    icon: AlertCircle,
                    label: 'To Do'
                };
        }
    };

    const statusInfo = getStatusInfo(currentStatus);
    const StatusIcon = statusInfo.icon;

    // Check if task is overdue
    const isOverdue = task.due_date && new Date(task.due_date) < new Date() && currentStatus !== 'done';

    return (
        <div className={`fixed inset-0 z-50 transition-all duration-300 ${open ? 'opacity-100 visible' : 'opacity-0 invisible'}`}>
            <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300"
                onClick={() => onOpenChange(false)}
            />
            <div className={`fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-4xl max-h-[90vh] overflow-hidden transition-all duration-300 ${open ? 'scale-100 opacity-100' : 'scale-95 opacity-0'}`}>
                <Card className="shadow-2xl border-0 bg-white dark:bg-gray-950">
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-b">
                        <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-3 mb-2">
                                    <div className={`p-2 rounded-lg ${getPriorityColor(task.priority)}`}>
                                        <Flag className="h-5 w-5" />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <CardTitle className="text-2xl font-bold text-gray-900 dark:text-gray-100 break-words">
                                            {task.title}
                                        </CardTitle>
                                        <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                                            <Briefcase className="h-4 w-4" />
                                            <span>
                                                {task.project?.name || project?.name || 'Inbox'}
                                                {task.boardName && ` • ${task.boardName}`}
                                                {task.listName && ` • ${task.listName}`}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {/* Status and Priority Badges */}
                                <div className="flex items-center gap-3 flex-wrap">
                                    {/* Interactive Status Dropdown */}
                                    <div className="flex items-center gap-2">
                                        <Select
                                            value={currentStatus}
                                            onValueChange={handleStatusChange}
                                            disabled={isUpdating}
                                        >
                                            <SelectTrigger
                                                className={`w-auto h-auto px-3 py-1 border-2 font-medium text-sm transition-all ${statusInfo.color} ${isUpdating ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-80 hover:scale-105 cursor-pointer'}`}
                                                title="Click to change task status"
                                            >
                                                <div className="flex items-center gap-1">
                                                    <StatusIcon className="h-3 w-3" />
                                                    <SelectValue />
                                                    {isUpdating ? (
                                                        <div className="ml-1 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                                    ) : (
                                                        <ChevronDown className="ml-1 h-3 w-3 opacity-50" />
                                                    )}
                                                </div>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="to_do" className="cursor-pointer">
                                                    <div className="flex items-center gap-2">
                                                        <AlertCircle className="h-4 w-4 text-gray-500" />
                                                        <span>To Do</span>
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="in_progress" className="cursor-pointer">
                                                    <div className="flex items-center gap-2">
                                                        <Timer className="h-4 w-4 text-blue-500" />
                                                        <span>In Progress</span>
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="done" className="cursor-pointer">
                                                    <div className="flex items-center gap-2">
                                                        <CheckCircle2 className="h-4 w-4 text-green-500" />
                                                        <span>Completed</span>
                                                    </div>
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <Badge variant="outline" className={`${getPriorityColor(task.priority)} font-medium px-3 py-1`}>
                                        <Flag className="h-3 w-3 mr-1" />
                                        {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)} Priority
                                    </Badge>
                                    {isOverdue && (
                                        <Badge variant="destructive" className="font-medium px-3 py-1 animate-pulse">
                                            <AlertCircle className="h-3 w-3 mr-1" />
                                            Overdue
                                        </Badge>
                                    )}
                                    {task.estimate && (
                                        <Badge variant="secondary" className="font-medium px-3 py-1">
                                            <TrendingUp className="h-3 w-3 mr-1" />
                                            {task.estimate}h estimated
                                        </Badge>
                                    )}
                                </div>

                                {/* Progress Bar for Status */}
                                <div className="mt-4">
                                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                                        <span>Progress</span>
                                        <span>
                                            {currentStatus === 'done' ? '100%' :
                                             currentStatus === 'in_progress' ? '50%' : '0%'}
                                        </span>
                                    </div>
                                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                        <div
                                            className={`h-2.5 rounded-full transition-all duration-700 ease-in-out ${
                                                currentStatus === 'done' ? 'bg-green-500 w-full' :
                                                currentStatus === 'in_progress' ? 'bg-blue-500 w-1/2' :
                                                'bg-gray-400 w-0'
                                            }`}
                                        />
                                    </div>
                                    {isUpdating && (
                                        <div className="mt-1 text-xs text-muted-foreground italic">
                                            Updating status...
                                        </div>
                                    )}
                                    {showSuccess && (
                                        <div className="mt-1 text-xs text-green-600 dark:text-green-400 flex items-center gap-1 animate-fade-in">
                                            <CheckCircle2 className="h-3 w-3" />
                                            Status updated successfully
                                        </div>
                                    )}
                                </div>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onOpenChange(false)}
                                className="h-8 w-8 p-0 hover:bg-white/50 dark:hover:bg-gray-800/50"
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent className="p-6 max-h-[60vh] overflow-y-auto">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            {/* Main Content */}
                            <div className="lg:col-span-2 space-y-6">
                                {/* Description */}
                                {task.description ? (
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <FileText className="h-4 w-4 text-muted-foreground" />
                                            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Description</h3>
                                        </div>
                                        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border">
                                            <p className="text-sm leading-relaxed whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                                                {task.description}
                                            </p>
                                        </div>
                                    </div>
                                ) : (
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <FileText className="h-4 w-4 text-muted-foreground" />
                                            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Description</h3>
                                        </div>
                                        <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-dashed">
                                            <p className="text-sm text-muted-foreground italic">
                                                No description provided
                                            </p>
                                        </div>
                                    </div>
                                )}

                                {/* Labels */}
                                {task.labels && task.labels.length > 0 && (
                                    <div className="space-y-3">
                                        <div className="flex items-center gap-2">
                                            <Tag className="h-4 w-4 text-muted-foreground" />
                                            <h3 className="font-semibold text-gray-900 dark:text-gray-100">Labels</h3>
                                        </div>
                                        <div className="flex flex-wrap gap-2">
                                            {task.labels.map((label: any) => (
                                                <Badge
                                                    key={label.id}
                                                    variant="outline"
                                                    className="px-3 py-1 font-medium"
                                                    style={{
                                                        backgroundColor: label.color + '15',
                                                        borderColor: label.color,
                                                        color: label.color
                                                    }}
                                                >
                                                    <Tag className="h-3 w-3 mr-1" />
                                                    {label.name}
                                                </Badge>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Sidebar */}
                            <div className="space-y-6">
                                {/* Task Details */}
                                <div className="space-y-4">
                                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                        <Clock className="h-4 w-4" />
                                        Task Details
                                    </h3>

                                    <div className="space-y-4">
                                        {/* Due Date */}
                                        {task.due_date && (
                                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
                                                <div className="flex items-center gap-2">
                                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                                    <span className="text-sm font-medium">Due Date</span>
                                                </div>
                                                <span className={`text-sm font-medium ${isOverdue ? 'text-red-600 dark:text-red-400' : 'text-gray-700 dark:text-gray-300'}`}>
                                                    {new Date(task.due_date).toLocaleDateString('en-US', {
                                                        weekday: 'short',
                                                        year: 'numeric',
                                                        month: 'short',
                                                        day: 'numeric'
                                                    })}
                                                </span>
                                            </div>
                                        )}

                                        {/* Duration */}
                                        {task.duration_days && task.duration_days > 1 && (
                                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
                                                <div className="flex items-center gap-2">
                                                    <Timer className="h-4 w-4 text-muted-foreground" />
                                                    <span className="text-sm font-medium">Duration</span>
                                                </div>
                                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {task.duration_days} day{task.duration_days !== 1 ? 's' : ''}
                                                </span>
                                            </div>
                                        )}

                                        {/* Estimate */}
                                        {task.estimate && (
                                            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
                                                <div className="flex items-center gap-2">
                                                    <Clock className="h-4 w-4 text-muted-foreground" />
                                                    <span className="text-sm font-medium">Estimate</span>
                                                </div>
                                                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {task.estimate}h
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Assignees */}
                                <div className="space-y-4">
                                    <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                        <Users className="h-4 w-4" />
                                        Assignees
                                    </h3>

                                    {task.assignees && task.assignees.length > 0 ? (
                                        <div className="space-y-2">
                                            {task.assignees.map((assignee: any) => (
                                                <div key={assignee.id} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
                                                    <Avatar className="h-8 w-8">
                                                        <AvatarImage src={assignee.avatar} />
                                                        <AvatarFallback className="text-sm font-medium">
                                                            {assignee.name.charAt(0).toUpperCase()}
                                                        </AvatarFallback>
                                                    </Avatar>
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                                                            {assignee.name}
                                                        </p>
                                                        {assignee.email && (
                                                            <p className="text-xs text-muted-foreground truncate">
                                                                {assignee.email}
                                                            </p>
                                                        )}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <div className="p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-dashed">
                                            <p className="text-sm text-muted-foreground italic">
                                                No assignees
                                            </p>
                                        </div>
                                    )}
                                </div>

                                {/* Created Info */}
                                {task.creator && (
                                    <div className="space-y-4">
                                        <h3 className="font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                            <User className="h-4 w-4" />
                                            Created By
                                        </h3>

                                        <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg border">
                                            <Avatar className="h-8 w-8">
                                                <AvatarImage src={task.creator.avatar} />
                                                <AvatarFallback className="text-sm font-medium">
                                                    {task.creator.name.charAt(0).toUpperCase()}
                                                </AvatarFallback>
                                            </Avatar>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                                    {task.creator.name}
                                                </p>
                                                {task.created_at && (
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(task.created_at).toLocaleDateString('en-US', {
                                                            weekday: 'short',
                                                            year: 'numeric',
                                                            month: 'short',
                                                            day: 'numeric',
                                                            hour: '2-digit',
                                                            minute: '2-digit'
                                                        })}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </CardContent>
                    <Separator />
                    <CardFooter className="bg-gray-50 dark:bg-gray-900/50 flex items-center justify-between p-6">
                        <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Clock className="h-4 w-4" />
                                <span>
                                    {task.updated_at ? (
                                        `Last updated ${new Date(task.updated_at).toLocaleDateString('en-US', {
                                            month: 'short',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}`
                                    ) : (
                                        `Created ${task.created_at ? new Date(task.created_at).toLocaleDateString('en-US', {
                                            month: 'short',
                                            day: 'numeric'
                                        }) : 'recently'}`
                                    )}
                                </span>
                            </div>

                            {/* Activity indicator for recently updated tasks */}
                            {task.updated_at && new Date(task.updated_at) > new Date(Date.now() - 24 * 60 * 60 * 1000) && (
                                <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
                                    <Activity className="h-3 w-3" />
                                    <span>Recently active</span>
                                </div>
                            )}
                        </div>

                        <div className="flex items-center gap-3">
                            {showSuccess && (
                                <div className="flex items-center gap-1 text-sm text-green-600 dark:text-green-400 animate-fade-in">
                                    <CheckCircle2 className="h-4 w-4" />
                                    <span>Changes saved</span>
                                </div>
                            )}
                            <Button
                                variant="outline"
                                onClick={() => onOpenChange(false)}
                                className="hover:bg-white dark:hover:bg-gray-800 transition-colors"
                                disabled={isUpdating}
                            >
                                Close
                            </Button>
                        </div>
                    </CardFooter>
                </Card>
            </div>
        </div>
    );
}
