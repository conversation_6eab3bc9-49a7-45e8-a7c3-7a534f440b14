import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Project, Task } from '@/types/project-manager';
import { X } from 'lucide-react';

interface TaskDetailModalProps {
    task: any;
    project?: Project;
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export default function TaskDetailModal({ task, project, open, onOpenChange }: TaskDetailModalProps) {
    if (!task) return null;

    // Get priority color styling
    const getPriorityColor = (priority: string) => {
        switch (priority) {
            case 'urgent': return 'border-red-500 bg-red-50 dark:bg-red-950/20';
            case 'high': return 'border-orange-500 bg-orange-50 dark:bg-orange-950/20';
            case 'medium': return 'border-yellow-500 bg-yellow-50 dark:bg-yellow-950/20';
            case 'low': return 'border-green-500 bg-green-50 dark:bg-green-950/20';
            default: return 'border-blue-500 bg-blue-50 dark:bg-blue-950/20';
        }
    };

    return (
        <div className={`fixed inset-0 z-50 ${open ? 'block' : 'hidden'}`}>
            <div className="fixed inset-0 bg-black/50" onClick={() => onOpenChange(false)} />
            <div className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <Card>
                    <CardHeader>
                        <div className="flex items-start justify-between">
                            <div className="flex-1">
                                <CardTitle className="text-xl">{task.title}</CardTitle>
                                <CardDescription className="mt-2">
                                    {task.project?.name || project?.name || 'Inbox'} 
                                    {task.boardName && ` • ${task.boardName}`}
                                    {task.listName && ` • ${task.listName}`}
                                </CardDescription>
                            </div>
                            <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => onOpenChange(false)}
                                className="h-8 w-8 p-0"
                            >
                                <X className="h-4 w-4" />
                            </Button>
                        </div>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        {/* Task Details */}
                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Priority</label>
                                <Badge variant="outline" className={`mt-1 ${getPriorityColor(task.priority)}`}>
                                    {task.priority}
                                </Badge>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Status</label>
                                <Badge variant="outline" className="mt-1 capitalize">
                                    {task.status.replace('_', ' ')}
                                </Badge>
                            </div>
                            {task.due_date && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                                    <p className="mt-1 text-sm">{new Date(task.due_date).toLocaleDateString()}</p>
                                </div>
                            )}
                            {task.duration_days && task.duration_days > 1 && (
                                <div>
                                    <label className="text-sm font-medium text-muted-foreground">Duration</label>
                                    <p className="mt-1 text-sm">{task.duration_days} days</p>
                                </div>
                            )}
                        </div>

                        {/* Description */}
                        {task.description && (
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Description</label>
                                <p className="mt-1 text-sm whitespace-pre-wrap">{task.description}</p>
                            </div>
                        )}

                        {/* Assignees */}
                        {task.assignees && task.assignees.length > 0 && (
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Assignees</label>
                                <div className="mt-2 flex flex-wrap gap-2">
                                    {task.assignees.map((assignee: any) => (
                                        <div key={assignee.id} className="flex items-center gap-2 bg-muted rounded-lg p-2">
                                            <Avatar className="h-6 w-6">
                                                <AvatarImage src={assignee.avatar} />
                                                <AvatarFallback className="text-xs">
                                                    {assignee.name.charAt(0).toUpperCase()}
                                                </AvatarFallback>
                                            </Avatar>
                                            <span className="text-sm">{assignee.name}</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Labels */}
                        {task.labels && task.labels.length > 0 && (
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Labels</label>
                                <div className="mt-2 flex flex-wrap gap-2">
                                    {task.labels.map((label: any) => (
                                        <Badge 
                                            key={label.id} 
                                            variant="outline"
                                            style={{ 
                                                backgroundColor: label.color + '20',
                                                borderColor: label.color,
                                                color: label.color
                                            }}
                                        >
                                            {label.name}
                                        </Badge>
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Created info */}
                        {task.creator && (
                            <div>
                                <label className="text-sm font-medium text-muted-foreground">Created by</label>
                                <div className="mt-2 flex items-center gap-2">
                                    <Avatar className="h-6 w-6">
                                        <AvatarImage src={task.creator.avatar} />
                                        <AvatarFallback className="text-xs">
                                            {task.creator.name.charAt(0).toUpperCase()}
                                        </AvatarFallback>
                                    </Avatar>
                                    <span className="text-sm">{task.creator.name}</span>
                                    {task.created_at && (
                                        <span className="text-xs text-muted-foreground">
                                            on {new Date(task.created_at).toLocaleDateString()}
                                        </span>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                    <CardFooter className="flex justify-end">
                        <Button onClick={() => onOpenChange(false)}>
                            Close
                        </Button>
                    </CardFooter>
                </Card>
            </div>
        </div>
    );
}
