@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    /* Improved balanced light theme */
    --background: oklch(0.97 0.005 240);
    --foreground: oklch(0.2 0.01 240);
    --card: oklch(0.99 0.002 240);
    --card-foreground: oklch(0.2 0.01 240);
    --popover: oklch(0.99 0.002 240);
    --popover-foreground: oklch(0.2 0.01 240);
    --primary: oklch(0.58 0.18 240); /* Vibrant blue */
    --primary-foreground: oklch(0.98 0.002 240);
    --secondary: oklch(0.94 0.01 240);
    --secondary-foreground: oklch(0.25 0.01 240);
    --muted: oklch(0.94 0.01 240);
    --muted-foreground: oklch(0.5 0.01 240);
    --accent: oklch(0.92 0.02 240);
    --accent-foreground: oklch(0.25 0.01 240);
    --destructive: oklch(0.62 0.25 25); /* Vibrant red */
    --destructive-foreground: oklch(0.98 0.002 240);
    --border: oklch(0.88 0.01 240);
    --input: oklch(0.94 0.01 240);
    --ring: oklch(0.58 0.18 240); /* Vibrant blue */
    --chart-1: oklch(0.58 0.18 240); /* Vibrant blue */
    --chart-2: oklch(0.62 0.25 25); /* Vibrant red */
    --chart-3: oklch(0.62 0.25 140); /* Vibrant green */
    --chart-4: oklch(0.62 0.25 60); /* Vibrant orange */
    --chart-5: oklch(0.58 0.18 280); /* Vibrant purple */
    --radius: 0.75rem;
    --sidebar: oklch(0.95 0.01 240);
    --sidebar-foreground: oklch(0.25 0.02 240);
    --sidebar-primary: oklch(0.58 0.18 240); /* Vibrant blue */
    --sidebar-primary-foreground: oklch(0.98 0.002 240);
    --sidebar-accent: oklch(0.88 0.03 240);
    --sidebar-accent-foreground: oklch(0.2 0.02 240);
    --sidebar-border: oklch(0.85 0.02 240);
    --sidebar-ring: oklch(0.58 0.18 240); /* Vibrant blue */
}

.dark {
    /* Improved balanced dark theme */
    --background: oklch(0.12 0.01 240);
    --foreground: oklch(0.92 0.005 240);
    --card: oklch(0.16 0.01 240);
    --card-foreground: oklch(0.92 0.005 240);
    --popover: oklch(0.16 0.01 240);
    --popover-foreground: oklch(0.92 0.005 240);
    --primary: oklch(0.65 0.2 240); /* Vibrant blue */
    --primary-foreground: oklch(0.08 0.01 240);
    --secondary: oklch(0.22 0.01 240);
    --secondary-foreground: oklch(0.88 0.005 240);
    --muted: oklch(0.22 0.01 240);
    --muted-foreground: oklch(0.65 0.01 240);
    --accent: oklch(0.28 0.02 240);
    --accent-foreground: oklch(0.88 0.005 240);
    --destructive: oklch(0.68 0.28 25); /* Vibrant red */
    --destructive-foreground: oklch(0.08 0.01 240);
    --border: oklch(0.28 0.01 240);
    --input: oklch(0.22 0.01 240);
    --ring: oklch(0.65 0.2 240); /* Vibrant blue */
    --chart-1: oklch(0.65 0.2 240); /* Vibrant blue */
    --chart-2: oklch(0.68 0.28 25); /* Vibrant red */
    --chart-3: oklch(0.68 0.28 140); /* Vibrant green */
    --chart-4: oklch(0.68 0.28 60); /* Vibrant orange */
    --chart-5: oklch(0.65 0.2 280); /* Vibrant purple */
    --sidebar: oklch(0.14 0.01 240);
    --sidebar-foreground: oklch(0.88 0.01 240);
    --sidebar-primary: oklch(0.65 0.2 240); /* Vibrant blue */
    --sidebar-primary-foreground: oklch(0.08 0.01 240);
    --sidebar-accent: oklch(0.25 0.02 240);
    --sidebar-accent-foreground: oklch(0.92 0.01 240);
    --sidebar-border: oklch(0.3 0.01 240);
    --sidebar-ring: oklch(0.65 0.2 240); /* Vibrant blue */
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
        font-feature-settings: "cv02", "cv03", "cv04", "cv11";
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    /* Enhanced sidebar styling */
    [data-sidebar="sidebar"] {
        background: var(--sidebar);
        border-right: 1px solid var(--sidebar-border);
    }

    /* Active state styling */
    [data-sidebar="sidebar"] [data-active="true"] {
        background: var(--sidebar-accent);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Apple-like hover effects */
.apple-hover {
    @apply transition-all duration-200 ease-out;
}

.apple-hover:hover {
    @apply scale-[1.02];
    background-color: color-mix(in srgb, var(--sidebar-accent) 60%, transparent);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.dark .apple-hover:hover {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

/* Apple-like card styling - hover effects removed, only for buttons */
.card {
    @apply shadow-sm transition-all duration-200 ease-out;
    backdrop-filter: blur(10px);
}

/* Card hover effects are now controlled by the hoverable prop in the Card component */

/* Disable hover effects on non-interactive elements */
.no-hover, .no-hover * {
    transition: none !important;
    transform: none !important;
    box-shadow: inherit !important;
    background-color: inherit !important;
    scale: none !important;
    filter: none !important;
}

.no-hover:hover, .no-hover *:hover {
    transition: none !important;
    transform: none !important;
    box-shadow: inherit !important;
    background-color: inherit !important;
    scale: none !important;
    filter: none !important;
}

/* Subtle hover effects for clickable but non-button elements */
.subtle-hover {
    transition: background-color 0.2s ease;
}

.subtle-hover:hover {
    background-color: color-mix(in srgb, var(--muted) 30%, transparent);
}

/* Apple-like button styling - enhanced for better hover effects */
.btn, .button, button, [role="button"] {
    @apply transition-all duration-200 ease-out;
}

.btn:hover, .button:hover, button:hover, [role="button"]:hover {
    @apply scale-[1.01];
    filter: brightness(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dark .btn:hover, .dark .button:hover, .dark button:hover, .dark [role="button"]:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Navigation item specific hover effects */
.nav-item {
    position: relative;
    isolation: isolate;
}

.nav-item::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    background: transparent;
    transition: all 0.2s ease-out;
    z-index: -1;
}

.nav-item:hover::before {
    background: color-mix(in srgb, var(--sidebar-accent) 70%, transparent);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.dark .nav-item:hover::before {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Prevent hover effects from bubbling */
.nav-item:hover {
    z-index: 1;
}

/* Apple-like interactive elements - removed, use button styling instead */

/* Prevent hover effects from affecting parent elements */
.hover-isolate {
    isolation: isolate;
    position: relative;
}

.hover-isolate > * {
    isolation: isolate;
}

/* Ensure hover effects only apply to direct targets */
button:hover,
a:hover,
[role="button"]:hover {
    isolation: isolate;
    z-index: 1;
}

/* Prevent hover state inheritance */
.no-hover-inherit * {
    pointer-events: auto;
}

.no-hover-inherit *:not(:hover) {
    background-color: inherit !important;
    transform: none !important;
    box-shadow: inherit !important;
}

/* Specific fix for navigation groups */
nav ul li:not(:hover) {
    background: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

nav ul:not(:hover) {
    background: transparent !important;
}

nav:not(:hover) {
    background: transparent !important;
}

/* Ensure only direct hover targets get effects */
.nav-item:not(:hover) {
    background: transparent !important;
    transform: none !important;
    box-shadow: none !important;
}

.nav-item:not(:hover)::before {
    background: transparent !important;
    box-shadow: none !important;
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: oklch(0.7 0 0 / 0.3);
    border-radius: 4px;
    transition: all 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: oklch(0.6 0 0 / 0.5);
}

.dark ::-webkit-scrollbar-thumb {
    background: oklch(0.4 0 0 / 0.4);
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.5 0 0 / 0.6);
}

/* Firefox scrollbar styling */
* {
    scrollbar-width: thin;
    scrollbar-color: oklch(0.7 0 0 / 0.3) transparent;
}

.dark * {
    scrollbar-color: oklch(0.4 0 0 / 0.4) transparent;
}

/* Enhanced sidebar scrollbar */
[data-sidebar="sidebar"] ::-webkit-scrollbar {
    width: 6px;
}

[data-sidebar="sidebar"] ::-webkit-scrollbar-thumb {
    background: oklch(0.6 0 0 / 0.2);
    border-radius: 3px;
}

[data-sidebar="sidebar"] ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.5 0 0 / 0.4);
}

.dark [data-sidebar="sidebar"] ::-webkit-scrollbar-thumb {
    background: oklch(0.5 0 0 / 0.3);
}

.dark [data-sidebar="sidebar"] ::-webkit-scrollbar-thumb:hover {
    background: oklch(0.6 0 0 / 0.5);
}
